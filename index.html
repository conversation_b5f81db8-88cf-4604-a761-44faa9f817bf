<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> | Cybersecurity Specialist & Penetration Tester</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">CyberSec</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#skills" class="nav-link">Skills</a></li>
                <li><a href="#experience" class="nav-link">Experience</a></li>
                <li><a href="#certifications" class="nav-link">Certifications</a></li>
                <li><a href="#projects" class="nav-link">Projects</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-main">Mohanad Ahmed</span>
                    <span class="title-sub">Usama</span>
                </h1>
                <p class="hero-subtitle">Cybersecurity Specialist & Penetration Tester</p>
                <p class="hero-description">
                    Computer Science Expert with specialized training in penetration testing 
                    and ethical hacking. Experienced in vulnerability assessment, network analysis, 
                    and web application security testing using industry-standard tools.
                </p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">View Portfolio</a>
                    <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="terminal-window">
                    <div class="terminal-header">
                        <div class="terminal-buttons">
                            <span class="btn-close"></span>
                            <span class="btn-minimize"></span>
                            <span class="btn-maximize"></span>
                        </div>
                        <span class="terminal-title">penetration-test.sh</span>
                    </div>
                    <div class="terminal-body">
                        <div class="terminal-line">
                            <span class="prompt">root@kali:~#</span>
                            <span class="command">nmap -sS -O target.com</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output">Starting Nmap scan...</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output success">22/tcp open ssh</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output success">80/tcp open http</span>
                        </div>
                        <div class="terminal-line">
                            <span class="output warning">443/tcp open https</span>
                        </div>
                        <div class="terminal-line">
                            <span class="prompt">root@kali:~#</span>
                            <span class="command typing">sqlmap -u "target.com" --dbs</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p>
                        As a highly motivated Computer Science Expert with a strong foundation in algorithms, 
                        software development, and database systems, I bring proven ability to apply Python, C#, 
                        and SQL in academic and personal projects. My specialized training in penetration testing 
                        includes hands-on experience with industry-standard tools like Wireshark and Burp Suite 
                        for vulnerability assessment and network analysis.
                    </p>
                    <p>
                        Currently pursuing my degree at Menoufia University with a GPA of 3.67, I have completed 
                        relevant coursework in Data Structures, Operating Systems, Software Engineering, Computer 
                        Networks, and Artificial Intelligence. I am eager to leverage my problem-solving and 
                        teamwork skills to contribute to dynamic tech teams and drive innovative security solutions.
                    </p>
                    <div class="stats">
                        <div class="stat-item">
                            <span class="stat-number">2</span>
                            <span class="stat-label">Security Certifications</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3.67</span>
                            <span class="stat-label">GPA</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">2026</span>
                            <span class="stat-label">Expected Graduation</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="mohanad_usama.jpg" alt="Mohanad Ahmed Usama" class="profile-photo">
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3>Penetration Testing & Security</h3>
                    <ul>
                        <li>Network Security</li>
                        <li>Vulnerability Assessment</li>
                        <li>Packet Analysis (Wireshark)</li>
                        <li>Web Application Testing (Burp Suite)</li>
                        <li>XSS/SQLi Detection</li>
                    </ul>
                </div>
                <div class="skill-category">
                    <h3>Security Tools</h3>
                    <div class="tools-grid">
                        <div class="tool-item">
                            <span class="tool-icon">🔍</span>
                            <span>Nmap</span>
                        </div>
                        <div class="tool-item">
                            <span class="tool-icon">⚡</span>
                            <span>Metasploit</span>
                        </div>
                        <div class="tool-item">
                            <span class="tool-icon">🔧</span>
                            <span>Burp Suite</span>
                        </div>
                        <div class="tool-item">
                            <span class="tool-icon">📊</span>
                            <span>Wireshark</span>
                        </div>
                        <div class="tool-item">
                            <span class="tool-icon">🐍</span>
                            <span>Python</span>
                        </div>
                        <div class="tool-item">
                            <span class="tool-icon">💻</span>
                            <span>Kali Linux</span>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>Programming & Development</h3>
                    <ul>
                        <li>Python - Advanced</li>
                        <li>C# - Proficient</li>
                        <li>SQL & SQLite - Database Management</li>
                        <li>Git & GitHub - Version Control</li>
                        <li>Visual Studio & VS Code</li>
                        <li>Object-Oriented Programming (OOP)</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Work Experience Section -->
    <section id="experience" class="experience">
        <div class="container">
            <h2 class="section-title">Professional Experience</h2>
            <div class="experience-grid">
                <div class="experience-card">
                    <div class="experience-header">
                        <div class="experience-title">
                            <h3>Research Member</h3>
                            <span class="experience-period">2022 - 2023</span>
                        </div>
                        <div class="experience-company">CompuMATH for Applied Research</div>
                    </div>
                    <div class="experience-content">
                        
                        
                        
                        <p>
                            Contributed to applied research initiatives in computational mathematics, 
                            collaborating on interdisciplinary projects that bridge theoretical concepts 
                            with practical applications in computer science and mathematical modeling.
                        </p>
                        <div class="experience-skills">
                            <span class="experience-skill">Research</span>
                            <span class="experience-skill">Mathematics</span>
                            <span class="experience-skill">Computational Analysis</span>
                        </div>
                    </div>
                </div>

                <div class="experience-card">
                    <div class="experience-header">
                        <div class="experience-title">
       
       
                            <h3>Active Member</h3>
                            <span class="experience-period">2023 - Present</span>
                        </div>
                        <div class="experience-company">Entrepreneurship Club, Menoufia University</div>
                    </div>
                    <div class="experience-content">
                        <p>
                            Active participant in entrepreneurship initiatives, developing business 
                            acumen and innovation skills while collaborating on startup projects 
                            and networking with industry professionals and fellow entrepreneurs.
                        </p>
                        <div class="experience-skills">
                            <span class="experience-skill">Business Development</span>
                            <span class="experience-skill">Innovation</span>
                            <span class="experience-skill">Networking</span>
                        </div>
                    </div>
                </div>

                <div class="experience-card">
                    <div class="experience-header">
                        <div class="experience-title">
                            <h3>Member</h3>
                            <span class="experience-period">Present</span>
                        </div>
                        <div class="experience-company">Computality Group, Menoufia University</div>
                    </div>
                    <div class="experience-content">
                        <p>
                            Contributing to computational research and development projects, 
                            focusing on advanced computing techniques and collaborative 
                            problem-solving in computer science applications and algorithms.
                        </p>
                        <div class="experience-skills">
                            <span class="experience-skill">Algorithm Design</span>
                            <span class="experience-skill">Problem Solving</span>
                            <span class="experience-skill">Team Collaboration</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Certifications Section -->
    <section id="certifications" class="certifications">
        <div class="container">
            <h2 class="section-title">Certifications</h2>
            <div class="certifications-grid">
                <div class="certification-card">
                    <div class="certification-header">
                        <h3>AWS Cloud Foundations</h3>
                        <span class="certification-type">Cloud Security</span>
                    </div>
                    <div class="certification-content">
                        <p>
                            Completed AWS Academy Cloud Foundations certification, demonstrating 
                            foundational knowledge of AWS cloud concepts, services, security 
                            architecture, pricing, and support structures essential for cloud security.
                        </p>
                        <div class="certification-tech">
                            <span class="tech-tag">AWS</span>
                            <span class="tech-tag">Cloud Security</span>
                            <span class="tech-tag">Architecture</span>
                            <span class="tech-tag">Best Practices</span>
                        </div>
                    </div>
                    <div class="certification-footer">
                        <a href="https://www.credly.com/badges/d015d467-63e7-4c78-842d-11fb9624f09a/public_url" target="_blank" class="btn btn-outline">View Certificate</a>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="certification-header">
                        <h3>Penetration Testing Training</h3>
                        <span class="certification-type">Security Assessment</span>
                    </div>
                    <div class="certification-content">
                        <p>
                            Completed comprehensive penetration testing and ethical hacking training 
                            through NTI, covering vulnerability assessment methodologies, network 
                            analysis, and web application security testing using industry-standard tools.
                        </p>
                        <div class="certification-tech">
                            <span class="tech-tag">Wireshark</span>
                            <span class="tech-tag">Burp Suite</span>
                            <span class="tech-tag">Nmap</span>
                            <span class="tech-tag">Metasploit</span>
                        </div>
                    </div>
                    <div class="certification-footer">
                        <button class="btn btn-outline">View Certificate</button>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="certification-header">
                        <h3>Introduction to Python</h3>
                        <span class="certification-type">Programming</span>
                    </div>
                    <div class="certification-content">
                        <p>
                            Completed Introduction to Python course through DataCamp, gaining 
                            foundational knowledge in Python programming including data types, 
                            control structures, functions, and basic data manipulation techniques 
                            essential for cybersecurity and data analysis applications.
                        </p>
                        <div class="certification-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">Programming</span>
                            <span class="tech-tag">Data Types</span>
                            <span class="tech-tag">Functions</span>
                        </div>
                    </div>
                    <div class="certification-footer">
                        <a href="https://www.datacamp.com/completed/statement-of-accomplishment/course/2e1f6d686d8d84578563b09948b4ad6c0af0706d" target="_blank" class="btn btn-outline">View Certificate</a>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="certification-header">
                        <h3>Intermediate Python</h3>
                        <span class="certification-type">Programming</span>
                    </div>
                    <div class="certification-content">
                        <p>
                            Completed Intermediate Python course through DataCamp, advancing skills 
                            in Python programming with focus on data structures, object-oriented 
                            programming, error handling, and advanced programming concepts essential 
                            for cybersecurity automation and data analysis.
                        </p>
                        <div class="certification-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">OOP</span>
                            <span class="tech-tag">Data Structures</span>
                            <span class="tech-tag">Error Handling</span>
                        </div>
                    </div>
                    <div class="certification-footer">
                        <a href="https://www.datacamp.com/completed/statement-of-accomplishment/course/2e1f6d686d8d84578563b09948b4ad6c0af0706d" target="_blank" class="btn btn-outline">View Certificate</a>
                    </div>
                </div>

                <div class="certification-card">
                    <div class="certification-header">
                        <h3>Understanding Data Science</h3>
                        <span class="certification-type">Data Science</span>
                    </div>
                    <div class="certification-content">
                        <p>
                            Completed Understanding Data Science course through DataCamp, gaining 
                            comprehensive knowledge of data science fundamentals including data analysis, 
                            statistical concepts, machine learning basics, and data visualization 
                            techniques essential for cybersecurity analytics and threat intelligence.
                        </p>
                        <div class="certification-tech">
                            <span class="tech-tag">Data Science</span>
                            <span class="tech-tag">Analytics</span>
                            <span class="tech-tag">Statistics</span>
                            <span class="tech-tag">Machine Learning</span>
                        </div>
                    </div>
                    <div class="certification-footer">
                        <a href="https://www.datacamp.com/completed/statement-of-accomplishment/course/bb2df860abf4cdf68a19b21f37b0b624ebab7a6a" target="_blank" class="btn btn-outline">View Certificate</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">Featured Projects</h2>
            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-header">
                        <h3>Library Management System</h3>
                        <span class="project-type">Desktop Application</span>
                    </div>
                    <div class="project-content">
                        <p>
                            Developed a comprehensive desktop application to efficiently manage student 
                            records and budget allocations. Implemented robust CRUD operations using 
                            SQLite, ensuring data integrity and accessibility with an intuitive 
                            WinForms interface.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">C#</span>
                            <span class="tech-tag">SQLite</span>
                            <span class="tech-tag">WinForms</span>
                            <span class="tech-tag">CRUD Operations</span>
                        </div>
                    </div>
                    <div class="project-footer">
                        <button class="btn btn-outline">View on GitHub</button>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-header">
                        <h3>Academic Coursework</h3>
                        <span class="project-type">Computer Science</span>
                    </div>
                    <div class="project-content">
                        <p>
                            Comprehensive computer science education covering Data Structures, 
                            Operating Systems, Software Engineering, Computer Networks, and 
                            Artificial Intelligence with a strong GPA of 3.67.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Data Structures</span>
                            <span class="tech-tag">Operating Systems</span>
                            <span class="tech-tag">Networks</span>
                            <span class="tech-tag">AI</span>
                        </div>
                    </div>
                    <div class="project-footer">
                        <button class="btn btn-outline">View Transcript</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Let's Connect and Collaborate</h3>
                    <p>
                        I'm actively seeking opportunities in cybersecurity and penetration testing. 
                        Whether you're looking for a dedicated team member or need security consulting, 
                        I'm ready to contribute my skills and passion for cybersecurity.
                    </p>
                    <div class="contact-methods">
                        <div class="contact-method">
                            <span class="contact-icon">📧</span>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-method">
                            <span class="contact-icon">📱</span>
                            <span>01020791946</span>
                        </div>
                        <div class="contact-method">
                            <span class="contact-icon">💼</span>
                            <a href="https://www.linkedin.com/in/mohanad-usama" target="_blank">www.linkedin.com/in/mohanad-usama
                            </a>
                        </div>
                        <div class="contact-method">
                            <span class="contact-icon">🐙</span>
                            <a href="https://github.com/MohanadUsama" target="_blank">github.com/MohanadUsama</a>
                        </div>
                        <div class="contact-method">
                            <span class="contact-icon">📍</span>
                            <span>Menofia, Egypt</span>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form>
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <input type="text" id="subject" name="subject" required>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
        <div class="footer-content">
            <p>&copy; 2025 Mohanad Ahmed Usama. All rights reserved.</p>
            <p>Securing the digital world, one vulnerability at a time.</p>
        </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>

















