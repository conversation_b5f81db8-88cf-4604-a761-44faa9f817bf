
/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark theme (default) */
    --primary-bg: #0a0a0a;
    --secondary-bg: #1a1a2e;
    --accent-bg: #16213e;
    --primary-color: #00d4ff;
    --secondary-color: #2ed573;
    --warning-color: #ff4757;
    --text-primary: #f1f2f6;
    --text-secondary: #a4b0be;
    --text-muted: #747d8c;
    --border-color: #2f3542;
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #2ed573 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --font-primary: 'Inter', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    --shadow-primary: 0 10px 30px rgba(0, 212, 255, 0.1);
    --shadow-secondary: 0 5px 15px rgba(0, 0, 0, 0.3);
    --navbar-bg: rgba(26, 26, 46, 0.95);
    --navbar-bg-scroll: rgba(26, 26, 46, 0.98);
}

/* Light theme */
[data-theme="light"] {
    --primary-bg: #ffffff;
    --secondary-bg: #f8f9fa;
    --accent-bg: #e9ecef;
    --primary-color: #0066cc;
    --secondary-color: #28a745;
    --warning-color: #dc3545;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --gradient-primary: linear-gradient(135deg, #0066cc 0%, #28a745 100%);
    --gradient-dark: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --shadow-primary: 0 10px 30px rgba(0, 102, 204, 0.1);
    --shadow-secondary: 0 5px 15px rgba(0, 0, 0, 0.1);
    --navbar-bg: rgba(248, 249, 250, 0.95);
    --navbar-bg-scroll: rgba(248, 249, 250, 0.98);
}

body {
    font-family: var(--font-primary);
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--navbar-bg);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-link.active {
    color: var(--primary-color);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
    transform: scale(1.1);
}

.theme-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(180deg);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--gradient-dark);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.1" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content {
    animation: slideInLeft 1s ease-out;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.title-main {
    display: block;
    color: var(--text-primary);
}

.title-sub {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--primary-bg);
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.2);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--primary-bg);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-outline:hover {
    border-color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
}

/* Terminal Window */
.hero-visual {
    animation: slideInRight 1s ease-out;
}

.terminal-window {
    background: var(--secondary-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-secondary);
    border: 1px solid var(--border-color);
}

.terminal-header {
    background: var(--accent-bg);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.terminal-buttons {
    display: flex;
    gap: 8px;
}

.terminal-buttons span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.btn-close {
    background: var(--warning-color);
}

.btn-minimize {
    background: #ffa502;
}

.btn-maximize {
    background: var(--secondary-color);
}

.terminal-title {
    color: var(--text-secondary);
    font-family: var(--font-mono);
    font-size: 0.9rem;
}

.terminal-body {
    padding: 20px;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.8;
}

.terminal-line {
    margin-bottom: 8px;
}

.prompt {
    color: var(--secondary-color);
    margin-right: 8px;
}

.command {
    color: var(--primary-color);
}

.output {
    color: var(--text-secondary);
}

.output.success {
    color: var(--secondary-color);
}

.output.warning {
    color: #ffa502;
}

.typing {
    animation: typing 2s steps(30) infinite;
}

@keyframes typing {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* About Section */
.about {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.about-image {
    display: flex;
    justify-content: center;
}

.image-placeholder {
    width: 250px;
    height: 300px;
    background: var(--accent-bg);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    border: 2px dashed var(--border-color);
}

/* Skills Section */
.skills {
    padding: 100px 0;
    background: var(--secondary-bg);
    
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.skill-category {
    background: var(--secondary-bg);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.skill-category h3 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.skill-category ul {
    list-style: none;
}

.skill-category li {
    color: var(--text-secondary);
    margin-bottom: 0.8rem;
    padding-left: 1rem;
    position: relative;
}

.skill-category li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.tool-item {
    background: var(--accent-bg);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.tool-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.tool-icon {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Projects Section */
.projects {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--accent-bg);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-secondary);
    border-color: var(--primary-color);
}

.project-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.project-header h3 {
    font-size: 1.2rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.project-type {
    background: var(--primary-color);
    color: var(--primary-bg);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-content {
    padding: 1.5rem;
}

.project-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: var(--secondary-bg);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.project-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Work Experience Section */
.experience {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.experience-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2.5rem;
}

.experience-card {
    background: var(--secondary-bg);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.experience-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.experience-card:hover::before {
    opacity: 1;
}

.experience-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-primary);
    border-color: var(--primary-color);
}

.experience-header {
    padding: 2rem 2rem 1rem 2rem;
    position: relative;
}

.experience-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.experience-title h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.experience-period {
    background: var(--gradient-primary);
    color: var(--primary-bg);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
}

.experience-company {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.experience-content {
    padding: 0 2rem 2rem 2rem;
}

.experience-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1rem;
}

.experience-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.experience-skill {
    background: var(--accent-bg);
    color: var(--text-secondary);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: var(--primary-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
}

.contact-icon {
    font-size: 1.2rem;
}

.contact-form {
    background: var(--secondary-bg);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    background: var(--accent-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-family: var(--font-primary);
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--secondary-bg);
    padding: 2rem 0;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    text-align: center;
    color: var(--text-muted);
}

.footer-content p {
    margin-bottom: 0.5rem;
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--secondary-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-secondary);
        padding: 2rem 0;
        border-top: 1px solid var(--border-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .theme-toggle {
        width: 35px;
        height: 35px;
    }

    .theme-icon {
        font-size: 1rem;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .stats {
        justify-content: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }
}

/* Tablet styles */
@media (max-width: 1024px) {
    .hero-container {
        gap: 3rem;
    }

    .about-content {
        gap: 3rem;
    }

    .contact-content {
        gap: 3rem;
    }

    .experience-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Small tablet and large mobile */
@media (max-width: 768px) {
    .section-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.3rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .terminal-window {
        margin: 0 1rem;
    }

    .experience-grid {
        grid-template-columns: 1fr;
    }

    .certifications-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .stats {
        flex-direction: column;
        gap: 1rem;
    }

    .tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .nav-container {
        padding: 0 15px;
    }

    .terminal-window {
        margin: 0;
    }

    .terminal-body {
        padding: 15px;
        font-size: 0.8rem;
    }

    .about-text p {
        font-size: 1rem;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

/* Extra small devices */
@media (max-width: 360px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }

    .theme-toggle {
        width: 32px;
        height: 32px;
    }

    .theme-icon {
        font-size: 0.9rem;
    }
}



/* Touch-friendly improvements */
.btn, .nav-link, .theme-toggle, .hamburger {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.btn {
    min-height: 44px; /* iOS recommended touch target size */
    min-width: 44px;
}

.nav-link {
    padding: 0.5rem 0;
    min-height: 44px;
    display: flex;
    align-items: center;
}

.tool-item, .project-card, .certification-card, .experience-card {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}

/* Smooth scrolling for all devices */
html {
    scroll-behavior: smooth;
}

/* Improved focus states for accessibility */
.btn:focus, .nav-link:focus, .theme-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.profile-photo {
    width: 250px;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: var(--shadow-primary);
    border: 2px solid var(--primary-color);
    transition: transform 0.3s ease;
}

@media (max-width: 768px) {
    .profile-photo {
        width: 200px;
        height: 240px;
    }
}

@media (max-width: 480px) {
    .profile-photo {
        width: 180px;
        height: 220px;
    }
}

/* Certifications Section */
.certifications {
    padding: 100px 0;
    background: var(--secondary-bg);
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.certification-card {
    background: var(--accent-bg);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.certification-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-secondary);
    border-color: var(--primary-color);
}

.certification-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.certification-header h3 {
    font-size: 1.2rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.certification-type {
    background: var(--primary-color);
    color: var(--primary-bg);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.certification-content {
    padding: 1.5rem;
}

.certification-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.certification-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.certification-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}



